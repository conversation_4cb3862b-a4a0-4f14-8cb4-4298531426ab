"""
Optimized Screenshot capture module for SnapCheck
Provides high-performance cross-platform screenshot functionality with region selection
Optimized for sub-1-second capture times and under 200MB memory usage
"""

import sys
import gc
import time
import platform
from typing import Optional, Dict, Any, Union
from PIL import Image
import mss
import psutil
from PySide6.QtWidgets import (QApplication, QWidget, QRubberBand)
from PySide6.QtCore import Qt, QRect, QPoint, Signal


class PerformanceMonitor:
    """Monitor performance metrics for screenshot operations"""

    def __init__(self):
        self.capture_times = []
        self.memory_usage = []
        self.last_capture_time = 0
        self.process = psutil.Process()

    def start_capture(self):
        """Start timing a capture operation and record memory usage"""
        self.last_capture_time = time.perf_counter()
        # Record memory usage at start
        memory_info = self.process.memory_info()
        self.memory_usage.append(memory_info.rss / 1024 / 1024)  # Convert to MB
        # Keep only last 10 measurements
        if len(self.memory_usage) > 10:
            self.memory_usage.pop(0)

    def end_capture(self):
        """End timing and record capture time"""
        if self.last_capture_time > 0:
            capture_time = time.perf_counter() - self.last_capture_time
            self.capture_times.append(capture_time)
            # Keep only last 10 measurements
            if len(self.capture_times) > 10:
                self.capture_times.pop(0)
            return capture_time
        return 0

    def get_average_capture_time(self) -> float:
        """Get average capture time"""
        return sum(self.capture_times) / len(self.capture_times) if self.capture_times else 0

    def get_current_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        memory_info = self.process.memory_info()
        return memory_info.rss / 1024 / 1024

    def get_average_memory_usage(self) -> float:
        """Get average memory usage in MB"""
        return sum(self.memory_usage) / len(self.memory_usage) if self.memory_usage else 0

    def is_performance_target_met(self) -> bool:
        """Check if performance targets are met"""
        avg_time = self.get_average_capture_time()
        current_memory = self.get_current_memory_usage()
        return avg_time < 1.0 and current_memory < 200  # Sub-1-second and under 200MB


class MemoryOptimizer:
    """Memory optimization utilities for image processing"""

    @staticmethod
    def optimize_image_for_memory(image: Image.Image, max_dimension: int = 2048) -> Image.Image:
        """
        Optimize image for memory usage while preserving quality for OCR

        Args:
            image: PIL Image to optimize
            max_dimension: Maximum width or height dimension

        Returns:
            Optimized PIL Image
        """
        # Get original dimensions
        width, height = image.size

        # Calculate scaling factor if image is too large
        if max(width, height) > max_dimension:
            scale_factor = max_dimension / max(width, height)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)

            # Use high-quality resampling for better OCR results
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # Convert to RGB if not already (removes alpha channel)
        if image.mode != 'RGB':
            image = image.convert('RGB')

        return image

    @staticmethod
    def cleanup_image_memory(image: Optional[Image.Image]) -> None:
        """Clean up image memory aggressively"""
        if image:
            try:
                image.close()
            except:
                pass  # Ignore errors if already closed
            del image

        # More aggressive garbage collection
        for _ in range(2):
            gc.collect()

    @staticmethod
    def get_image_memory_size(image: Image.Image) -> float:
        """Get approximate memory size of image in MB"""
        width, height = image.size
        channels = len(image.getbands())
        # Approximate memory usage: width * height * channels * bytes_per_pixel
        memory_bytes = width * height * channels * 4  # Assuming 4 bytes per pixel
        return memory_bytes / 1024 / 1024  # Convert to MB


class OptimizedScreenCapture:
    """Optimized screen capture with memory management"""

    def __init__(self):
        self._sct = None
        self._monitor_cache = None
        self._last_cleanup = time.time()
        self.performance_monitor = PerformanceMonitor()

    def __enter__(self):
        """Context manager entry"""
        if self._sct is None:
            self._sct = mss.mss()
        return self

    def __exit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:
        """Context manager exit with cleanup"""
        self.cleanup()

    def cleanup(self) -> None:
        """Clean up resources and force aggressive garbage collection"""
        if self._sct:
            self._sct.close()
            self._sct = None
        self._monitor_cache = None

        # More aggressive garbage collection
        for _ in range(3):
            gc.collect()

        self._last_cleanup = time.time()

    def _ensure_connection(self) -> None:
        """Ensure MSS connection is active"""
        if self._sct is None:
            self._sct = mss.mss()

    def _get_monitors(self) -> list:
        """Get monitors with caching"""
        if self._monitor_cache is None:
            self._ensure_connection()
            if self._sct:
                self._monitor_cache = self._sct.monitors
            else:
                self._monitor_cache = []
        return self._monitor_cache

    def capture_region(self, region: Dict[str, int], optimize_memory: bool = True) -> Optional[Image.Image]:
        """
        Capture a specific region with optimized memory usage

        Args:
            region: Dict with 'top', 'left', 'width', 'height' keys
            optimize_memory: Whether to apply memory optimizations

        Returns:
            PIL Image or None if failed
        """
        self.performance_monitor.start_capture()

        try:
            self._ensure_connection()

            if not self._sct:
                return None

            # Capture the region directly (no full screen capture)
            screenshot = self._sct.grab(region)

            # Convert to PIL Image efficiently
            # Use frombuffer for better memory efficiency
            img = Image.frombuffer(
                "RGB",
                screenshot.size,
                screenshot.bgra,
                "raw",
                "BGRX",
                0,
                1
            )

            # Create a copy to ensure we own the memory
            img_copy = img.copy()

            # Clean up the screenshot object immediately
            del screenshot
            del img  # Clean up the original image reference

            # Apply memory optimizations if requested
            if optimize_memory:
                img_copy = MemoryOptimizer.optimize_image_for_memory(img_copy)

            self.performance_monitor.end_capture()

            # Periodic cleanup to prevent memory leaks
            if time.time() - self._last_cleanup > 30:  # Cleanup every 30 seconds
                self.cleanup()

            return img_copy

        except Exception as e:
            print(f"Error capturing region: {e}")
            self.performance_monitor.end_capture()
            return None

    def capture_full_screen(self, monitor_index: int = 1) -> Optional[Image.Image]:
        """
        Capture full screen with memory optimization

        Args:
            monitor_index: Monitor to capture (1 = primary)

        Returns:
            PIL Image or None if failed
        """
        try:
            monitors = self._get_monitors()
            if monitor_index >= len(monitors):
                monitor_index = 1

            monitor = monitors[monitor_index]
            return self.capture_region(monitor)

        except Exception as e:
            print(f"Error capturing full screen: {e}")
            return None


class OptimizedScreenshotSelector(QWidget):
    """Optimized overlay widget for selecting screenshot region with memory management"""

    screenshot_taken = Signal(object)  # Emits PIL Image

    def __init__(self):
        super().__init__()
        self.setWindowFlags(Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.FramelessWindowHint | Qt.WindowType.Tool)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setStyleSheet("background-color: rgba(0, 0, 0, 100);")

        # Selection variables
        self.start_point = QPoint()
        self.end_point = QPoint()
        self.selecting = False
        self.rubber_band = QRubberBand(QRubberBand.Shape.Rectangle, self)

        # Get screen geometry
        screen = QApplication.primaryScreen()
        self.screen_geometry = screen.geometry()
        self.setGeometry(self.screen_geometry)

        # Optimized capture system
        self.capture_system = OptimizedScreenCapture()
        self.monitor_info = None
        self._get_monitor_info()

    def _get_monitor_info(self):
        """Get monitor information for optimized capture"""
        try:
            with self.capture_system as capture:
                monitors = capture._get_monitors()
                if len(monitors) > 1:
                    self.monitor_info = monitors[1]  # Primary monitor
                else:
                    # Fallback to screen geometry
                    self.monitor_info = {
                        'top': self.screen_geometry.top(),
                        'left': self.screen_geometry.left(),
                        'width': self.screen_geometry.width(),
                        'height': self.screen_geometry.height()
                    }
        except Exception as e:
            print(f"Error getting monitor info: {e}")
            # Fallback to screen geometry
            self.monitor_info = {
                'top': self.screen_geometry.top(),
                'left': self.screen_geometry.left(),
                'width': self.screen_geometry.width(),
                'height': self.screen_geometry.height()
            }
    
    def mousePressEvent(self, event):
        """Start selection"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.start_point = event.position().toPoint()
            self.rubber_band.setGeometry(QRect(self.start_point, self.start_point))
            self.rubber_band.show()
            self.selecting = True

    def mouseMoveEvent(self, event):
        """Update selection rectangle"""
        if self.selecting:
            self.end_point = event.position().toPoint()
            self.rubber_band.setGeometry(QRect(self.start_point, self.end_point).normalized())

    def mouseReleaseEvent(self, event):
        """Finish selection and capture region directly"""
        if event.button() == Qt.MouseButton.LeftButton and self.selecting:
            self.end_point = event.position().toPoint()
            self.selecting = False
            self.rubber_band.hide()

            # Calculate selection rectangle
            selection_rect = QRect(self.start_point, self.end_point).normalized()

            if selection_rect.width() > 10 and selection_rect.height() > 10:
                self._capture_and_emit(selection_rect)

            self.close()

    def keyPressEvent(self, event):
        """Handle escape key to cancel"""
        if event.key() == Qt.Key.Key_Escape:
            self.close()

    def _capture_and_emit(self, rect: QRect):
        """Capture the selected region directly without storing full screen"""
        if not self.monitor_info:
            return

        try:
            # Convert QRect to capture region relative to monitor
            capture_region = {
                'top': self.monitor_info['top'] + rect.top(),
                'left': self.monitor_info['left'] + rect.left(),
                'width': rect.width(),
                'height': rect.height()
            }

            # Capture the region directly using optimized capture
            with self.capture_system as capture:
                captured_image = capture.capture_region(capture_region)

            if captured_image:
                self.screenshot_taken.emit(captured_image)

        except Exception as e:
            print(f"Error capturing region: {e}")

    def closeEvent(self, event):
        """Clean up resources when closing"""
        self.capture_system.cleanup()
        super().closeEvent(event)


class ScreenshotCapture:
    """Optimized main screenshot capture class with memory management"""

    def __init__(self):
        self.selector = None
        self.capture_system = OptimizedScreenCapture()

    def capture_region(self, callback=None):
        """
        Start region selection for screenshot capture

        Args:
            callback: Function to call with the captured PIL Image
        """
        try:
            # Create and show optimized selector
            self.selector = OptimizedScreenshotSelector()

            if callback:
                self.selector.screenshot_taken.connect(callback)

            self.selector.show()
            self.selector.raise_()
            self.selector.activateWindow()

            return self.selector

        except Exception as e:
            print(f"Error starting screenshot capture: {e}")
            return None

    def capture_full_screen(self) -> Optional[Image.Image]:
        """
        Capture the full screen with optimization

        Returns:
            PIL Image of the full screen or None if failed
        """
        try:
            with self.capture_system as capture:
                return capture.capture_full_screen()

        except Exception as e:
            print(f"Error capturing full screen: {e}")
            return None

    def get_screen_info(self):
        """Get information about available screens with caching"""
        try:
            with self.capture_system as capture:
                monitors = capture._get_monitors()
                return {
                    'count': len(monitors) - 1,  # Exclude the "all monitors" entry
                    'monitors': monitors[1:],  # Skip the combined monitor
                    'primary': monitors[1] if len(monitors) > 1 else None,
                    'performance': {
                        'average_capture_time': capture.performance_monitor.get_average_capture_time(),
                        'target_met': capture.performance_monitor.is_performance_target_met()
                    }
                }
        except Exception as e:
            print(f"Error getting screen info: {e}")
            return {'count': 0, 'monitors': [], 'primary': None, 'performance': {}}

    def cleanup(self):
        """Clean up all resources"""
        if self.selector:
            self.selector.close()
            self.selector = None
        self.capture_system.cleanup()

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics"""
        monitor = self.capture_system.performance_monitor
        return {
            'average_capture_time': monitor.get_average_capture_time(),
            'current_memory_usage_mb': monitor.get_current_memory_usage(),
            'average_memory_usage_mb': monitor.get_average_memory_usage(),
            'target_met': monitor.is_performance_target_met(),
            'capture_count': len(monitor.capture_times),
            'memory_target_met': monitor.get_current_memory_usage() < 200,
            'performance_summary': {
                'capture_time_target': '< 1.0s',
                'memory_target': '< 200MB',
                'status': 'PASS' if monitor.is_performance_target_met() else 'FAIL'
            }
        }

    def force_memory_cleanup(self) -> Dict[str, float]:
        """Force memory cleanup and return before/after memory usage"""
        monitor = self.capture_system.performance_monitor
        before_memory = monitor.get_current_memory_usage()

        # Force cleanup
        self.cleanup()
        gc.collect()

        after_memory = monitor.get_current_memory_usage()

        return {
            'before_cleanup_mb': before_memory,
            'after_cleanup_mb': after_memory,
            'memory_freed_mb': before_memory - after_memory
        }


# Compatibility alias for backward compatibility
ScreenshotSelector = OptimizedScreenshotSelector


# Test and benchmark functions
def test_screenshot():
    """Test the optimized screenshot functionality"""
    app = QApplication(sys.argv)

    def on_screenshot(image):
        print(f"Screenshot captured: {image.size}")
        image.save("test_screenshot.png")
        print("Screenshot saved as test_screenshot.png")

        # Show performance stats
        stats = capture.get_performance_stats()
        print(f"Performance stats: {stats}")

        app.quit()

    capture = ScreenshotCapture()
    capture.capture_region(on_screenshot)

    sys.exit(app.exec())


def benchmark_capture():
    """Comprehensive benchmark of capture performance and memory usage"""
    capture = ScreenshotCapture()

    print("Benchmarking screenshot capture performance and memory usage...")
    print("=" * 60)

    # Initial memory state
    initial_stats = capture.get_performance_stats()
    print(f"Initial memory usage: {initial_stats['current_memory_usage_mb']:.1f} MB")

    # Test full screen capture with memory monitoring
    print("\nTesting full screen captures...")
    captured_images = []

    start_time = time.perf_counter()
    for i in range(5):
        img = capture.capture_full_screen()
        if img:
            img_size_mb = MemoryOptimizer.get_image_memory_size(img)
            print(f"Capture {i+1}: {img.size} ({img_size_mb:.1f} MB)")
            captured_images.append(img)
        else:
            print(f"Capture {i+1}: Failed")
    end_time = time.perf_counter()

    avg_time = (end_time - start_time) / 5
    print(f"\nAverage capture time: {avg_time:.3f}s")
    print(f"Time target met (< 1s): {avg_time < 1.0}")

    # Show detailed performance stats
    stats = capture.get_performance_stats()
    print(f"\nDetailed Performance Stats:")
    print(f"  Current memory usage: {stats['current_memory_usage_mb']:.1f} MB")
    print(f"  Average memory usage: {stats['average_memory_usage_mb']:.1f} MB")
    print(f"  Memory target met (< 200MB): {stats['memory_target_met']}")
    print(f"  Overall performance: {stats['performance_summary']['status']}")

    # Test memory cleanup
    print(f"\nTesting memory cleanup...")
    cleanup_stats = capture.force_memory_cleanup()
    print(f"  Memory before cleanup: {cleanup_stats['before_cleanup_mb']:.1f} MB")
    print(f"  Memory after cleanup: {cleanup_stats['after_cleanup_mb']:.1f} MB")
    print(f"  Memory freed: {cleanup_stats['memory_freed_mb']:.1f} MB")

    # Clean up captured images
    for img in captured_images:
        MemoryOptimizer.cleanup_image_memory(img)

    # Final cleanup
    capture.cleanup()

    print("\nBenchmark completed!")
    print("=" * 60)


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "benchmark":
        benchmark_capture()
    else:
        test_screenshot()
